class Favori {
  final int id;              // id du contenu (film/musique)
  final String title;
  final String imageUrl;
  final String category;

  const Favori({
    required this.id,
    required this.title,
    required this.imageUrl,
    required this.category,
  });

  Map<String, dynamic> toMap() => {
    'id': id,
    'title': title,
    'imageUrl': imageUrl,
    'category': category,
  };
}
