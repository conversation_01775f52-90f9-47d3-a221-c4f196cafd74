import '../data/app_db.dart';
import '../models/favori.dart';

class FavorisRepo {
  final _db = AppDB.instance;

  Future<bool> toggle(Favori f) async {
    if (await _db.favExists(f.id)) {
      await _db.favDelete(f.id);
      return false;
    } else {
      await _db.favInsert(f.toMap());
      return true;
    }
  }

  Future<List<Favori>> all() async {
    final rows = await _db.favAll();
    return rows.map((m) => Favori(
      id: (m['id'] as num).toInt(),
      title: m['title'] as String,
      imageUrl: m['imageUrl'] as String,
      category: m['category'] as String,
    )).toList();
  }
}
