import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as p;

class AppDB {
  AppDB._();
  static final AppDB instance = AppDB._();
  static Database? _db;

  Future<Database> _open() async {
    if (_db != null) return _db!;
    final path = p.join(await getDatabasesPath(), 'mon_spotify.db');
    _db = await openDatabase(
      path,
      version: 1,
      onCreate: (db, v) async {
        await db.execute('''
          CREATE TABLE favoris(
            id INTEGER PRIMARY KEY,
            title TEXT NOT NULL,
            imageUrl TEXT NOT NULL,
            category TEXT NOT NULL
          );
        ''');
        await db.execute('''
          CREATE TABLE playlists(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            isPublic INTEGER NOT NULL DEFAULT 0
          );
        ''');
        await db.execute('''
          CREATE TABLE playlist_items(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            playlist_id INTEGER NOT NULL,
            content_id INTEGER NOT NULL,
            content_type TEXT NOT NULL DEFAULT 'media',
            ord INTEGER NOT NULL DEFAULT 0,
            FOREIGN KEY(playlist_id) REFERENCES playlists(id) ON DELETE CASCADE
          );
        ''');
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_fav_cat ON favoris(category);');
      },
    );
    return _db!;
  }

  // ---------- Favoris
  Future<void> favInsert(Map<String, dynamic> row) async {
    final db = await _open();
    await db.insert('favoris', row, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<List<Map<String, dynamic>>> favAll() async {
    final db = await _open();
    return db.query('favoris', orderBy: 'id DESC');
  }

  Future<void> favDelete(int id) async {
    final db = await _open();
    await db.delete('favoris', where: 'id=?', whereArgs: [id]);
  }

  Future<bool> favExists(int id) async {
    final db = await _open();
    final r = await db.query('favoris', where: 'id=?', whereArgs: [id], limit: 1);
    return r.isNotEmpty;
  }

  // ---------- Playlists
  Future<int> plCreate(String name, {bool isPublic = false}) async {
    final db = await _open();
    return db.insert('playlists', {'name': name, 'isPublic': isPublic ? 1 : 0});
  }

  Future<List<Map<String, dynamic>>> plAll() async {
    final db = await _open();
    return db.query('playlists', orderBy: 'id DESC');
  }

  Future<void> plDelete(int id) async {
    final db = await _open();
    await db.delete('playlists', where: 'id=?', whereArgs: [id]);
  }

  Future<void> plAddItem(int playlistId, int contentId, {int ord = 0}) async {
    final db = await _open();
    await db.insert('playlist_items', {
      'playlist_id': playlistId,
      'content_id': contentId,
      'ord': ord,
    });
  }

  Future<List<Map<String, dynamic>>> plItems(int playlistId) async {
    final db = await _open();
    return db.query('playlist_items',
        where: 'playlist_id=?', whereArgs: [playlistId], orderBy: 'ord ASC');
  }
}
