import 'package:flutter/material.dart';
import '../data/favoris db.dart';
import '../models/favori.dart';

class FavorisPage extends StatefulWidget {
  const FavorisPage({super.key});

  @override
  State<FavorisPage> createState() => _FavorisPageState();
}

class _FavorisPageState extends State<FavorisPage> {
  List<Favori> favoris = [];

  @override
  void initState() {
    super.initState();
    _loadFavoris();
  }

  Future<void> _loadFavoris() async {
    final favs = await FavorisDB.getFavoris();
    setState(() {
      favoris = favs;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Mes Favoris")),
      body: ListView.builder(
        itemCount: favoris.length,
        itemBuilder: (context, i) {
          final f = favoris[i];
          return ListTile(
            leading: Image.asset(f.imageUrl, width: 50, fit: BoxFit.cover),
            title: Text(f.title),
            subtitle: Text(f.category),
            trailing: Icon<PERSON>utton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () async {
                await FavorisDB.deleteFavori(f.id);
                _loadFavoris();
              },
            ),
          );
        },
      ),
    );
  }
}
