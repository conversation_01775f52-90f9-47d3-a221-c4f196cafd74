# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\spotify\\mon_spotify" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\flutter\\flutter"
  "PROJECT_DIR=D:\\spotify\\mon_spotify"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\spotify\\mon_spotify\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\spotify\\mon_spotify"
  "FLUTTER_TARGET=D:\\spotify\\mon_spotify\\lib\\main.dart"
  "DART_DEFINES=Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl,RkxVVFRFUl9WRVJTSU9OPTMuMzUuMw==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YTQwMmQ5YTQzNw==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGRmNDdkZDNmZg==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjI="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\spotify\\mon_spotify\\.dart_tool\\package_config.json"
)
