// lib/data/favoris_db.dart
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as p;
import '../models/favori.dart';

class FavorisDB {
  FavorisDB._();                           // ctor privé
  static final FavorisDB instance = FavorisDB._();
  static Database? _db;

  /// Ouvre (ou renvoie) la base ; on ne l’ouvre qu’une fois.
  Future<Database> _openDB() async {
    if (_db != null) return _db!;

    final dbPath = await getDatabasesPath();
    final file = p.join(dbPath, 'favoris.db');

    _db = await openDatabase(
      file,
      version: 1,
      onCreate: (db, version) async {
        await db.execute('''
          CREATE TABLE favoris(
            id INTEGER PRIMARY KEY,
            title TEXT NOT NULL,
            imageUrl TEXT NOT NULL,
            category TEXT NOT NULL
          );
        ''');
        // index utile si tu cherches par category
        await db.execute('CREATE INDEX IF NOT EXISTS idx_fav_cat ON favoris(category);');
      },
    );
    return _db!;
  }

  /// INSERT or REPLACE
  Future<void> insertFavori(Favori f) async {
    final db = await _openDB();
    await db.insert(
      'favoris',
      f.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Favori>> getFavoris() async {
    final db = await _openDB();
    final maps = await db.query('favoris', orderBy: 'id DESC');
    return maps.map((m) => Favori(
      id: (m['id'] as num).toInt(),
      title: m['title'] as String,
      imageUrl: m['imageUrl'] as String,
      category: m['category'] as String,
    )).toList();
  }

  Future<void> deleteFavori(int id) async {
    final db = await _openDB();
    await db.delete('favoris', where: 'id = ?', whereArgs: [id]);
  }

  /// Petit utilitaire: savoir si un contenu est déjà favori
  Future<bool> exists(int id) async {
    final db = await _openDB();
    final r = await db.query('favoris', where: 'id = ?', whereArgs: [id], limit: 1);
    return r.isNotEmpty;
  }

  /// Toggle favori (ajoute si absent, supprime si présent)
  Future<bool> toggle(Favori f) async {
    if (await exists(f.id)) {
      await deleteFavori(f.id);
      return false;
    } else {
      await insertFavori(f);
      return true;
    }
  }
}
