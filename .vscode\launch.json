{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Dart: Run all Tests",
            "type": "dart",
            "request": "launch",
            "program": "./test/"
        },
        {
            "name": "mon_spotify",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "mon_spotify (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "mon_spotify (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}